import React from 'react';
import { motion } from 'motion/react';
import { cn } from '@/src/background/util';
import type { ChatStatus } from '../types/config';

interface LoadingIndicatorProps {
  chatStatus: ChatStatus;
  persona: string;
  className?: string;
}

// Persona-specific loading messages for each chat status
const personaLoadingMessages: Record<string, Record<ChatStatus, string[]>> = {
  Scholar: {
    idle: ['Ready to research'],
    typing: ['Preparing analysis'],
    searching: ['Researching sources', 'Gathering academic data', 'Reviewing literature'],
    reading: ['Analyzing page content', 'Examining documentation', 'Processing information'],
    thinking: ['Formulating analysis', 'Structuring findings', 'Preparing comprehensive response'],
    done: ['Analysis complete']
  },
  Executive: {
    idle: ['Ready to analyze'],
    typing: ['Preparing assessment'],
    searching: ['Analyzing market data', 'Gathering business intelligence', 'Processing insights'],
    reading: ['Reviewing content', 'Analyzing documentation', 'Processing strategic data'],
    thinking: ['Formulating strategy', 'Preparing executive summary', 'Developing recommendations'],
    done: ['Analysis complete']
  },
  Storyteller: {
    idle: ['Ready to create'],
    typing: ['Preparing narrative'],
    searching: ['Gathering inspiration', 'Collecting story elements', 'Finding creative angles'],
    reading: ['Absorbing content', 'Understanding context', 'Drawing connections'],
    thinking: ['Crafting narrative', 'Weaving story elements', 'Bringing ideas to life'],
    done: ['Story ready']
  },
  Skeptic: {
    idle: ['Ready to question'],
    typing: ['Preparing analysis'],
    searching: ['Fact-checking sources', 'Verifying information', 'Cross-referencing data'],
    reading: ['Scrutinizing content', 'Examining claims', 'Checking for bias'],
    thinking: ['Evaluating evidence', 'Questioning assumptions', 'Forming critical assessment'],
    done: ['Analysis complete']
  },
  Mentor: {
    idle: ['Ready to guide'],
    typing: ['Preparing guidance'],
    searching: ['Finding learning resources', 'Gathering educational content', 'Collecting examples'],
    reading: ['Understanding your needs', 'Reviewing context', 'Identifying opportunities'],
    thinking: ['Crafting guidance', 'Preparing explanations', 'Developing teaching approach'],
    done: ['Guidance ready']
  },
  Investigator: {
    idle: ['Ready to investigate'],
    typing: ['Starting investigation'],
    searching: ['Gathering evidence', 'Following leads', 'Collecting information'],
    reading: ['Examining details', 'Analyzing evidence', 'Connecting dots'],
    thinking: ['Piecing together findings', 'Drawing conclusions', 'Preparing report'],
    done: ['Investigation complete']
  },
  Pragmatist: {
    idle: ['Ready to solve'],
    typing: ['Preparing solution'],
    searching: ['Finding practical solutions', 'Gathering actionable data', 'Identifying tools'],
    reading: ['Reviewing practical details', 'Understanding requirements', 'Assessing feasibility'],
    thinking: ['Developing practical approach', 'Creating actionable plan', 'Preparing recommendations'],
    done: ['Solution ready']
  },
  Enthusiast: {
    idle: ['Ready to explore'],
    typing: ['Getting excited'],
    searching: ['Discovering amazing content', 'Finding exciting information', 'Exploring possibilities'],
    reading: ['Diving into details', 'Absorbing fascinating content', 'Getting inspired'],
    thinking: ['Connecting exciting ideas', 'Preparing enthusiastic response', 'Sharing discoveries'],
    done: ['Ready to share']
  },
  Curator: {
    idle: ['Ready to curate'],
    typing: ['Preparing collection'],
    searching: ['Curating quality sources', 'Selecting best content', 'Organizing information'],
    reading: ['Evaluating content quality', 'Assessing relevance', 'Categorizing information'],
    thinking: ['Organizing findings', 'Creating structured overview', 'Preparing curated response'],
    done: ['Curation complete']
  },
  Friend: {
    idle: ['Ready to chat'],
    typing: ['Thinking'],
    searching: ['Looking into this', 'Checking things out', 'Finding helpful info'],
    reading: ['Reading through this', 'Understanding the situation', 'Getting up to speed'],
    thinking: ['Thinking about this', 'Putting thoughts together', 'Getting back to you'],
    done: ['Ready to respond']
  }
};

// Fallback messages for unknown personas
const defaultLoadingMessages: Record<ChatStatus, string[]> = {
  idle: ['Ready'],
  typing: ['Preparing'],
  searching: ['Searching the web', 'Gathering information', 'Finding relevant content'],
  reading: ['Reading content', 'Processing information', 'Analyzing data'],
  thinking: ['Processing', 'Analyzing', 'Preparing response'],
  done: ['Complete']
};

const LoadingDots: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('flex space-x-1', className)}>
    {[0, 1, 2].map((i) => (
      <motion.div
        key={i}
        className="w-1 h-1 bg-current rounded-full opacity-60"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.6, 1, 0.6],
        }}
        transition={{
          duration: 1.2,
          repeat: Infinity,
          delay: i * 0.2,
          ease: "easeInOut",
        }}
      />
    ))}
  </div>
);

export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  chatStatus,
  persona,
  className
}) => {
  // Get persona-specific messages or fall back to default
  const personaMessages = personaLoadingMessages[persona] || defaultLoadingMessages;
  const statusMessages = personaMessages[chatStatus] || defaultLoadingMessages[chatStatus];
  
  // Cycle through messages for the current status
  const [messageIndex, setMessageIndex] = React.useState(0);
  
  React.useEffect(() => {
    if (statusMessages.length <= 1) return;
    
    const interval = setInterval(() => {
      setMessageIndex((prev) => (prev + 1) % statusMessages.length);
    }, 5000); // Change message every 2 seconds
    
    return () => clearInterval(interval);
  }, [statusMessages.length, chatStatus]);
  
  // Reset message index when status changes
  React.useEffect(() => {
    setMessageIndex(0);
  }, [chatStatus]);
  
  const currentMessage = statusMessages[messageIndex] || statusMessages[0] || 'Processing...';
  
  return (
    <motion.div
      className={cn(
        'flex items-center space-x-2 text-muted-foreground/70 text-sm',
        'px-4 py-3 max-w-[85%]',
        className
      )}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <motion.span
        key={`${chatStatus}-${messageIndex}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.4 }}
        className="font-medium whitespace-nowrap"
      >
        {currentMessage}
      </motion.span>
      <LoadingDots />
    </motion.div>
  );
};
